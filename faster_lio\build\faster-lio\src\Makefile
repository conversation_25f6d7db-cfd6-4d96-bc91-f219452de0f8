# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/faster_lio/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/faster_lio/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/faster_lio/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/faster_lio/build/CMakeFiles /home/<USER>/faster_lio/build/faster-lio/src/CMakeFiles/progress.marks
	cd /home/<USER>/faster_lio/build && $(MAKE) -f CMakeFiles/Makefile2 faster-lio/src/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/faster_lio/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/faster_lio/build && $(MAKE) -f CMakeFiles/Makefile2 faster-lio/src/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/faster_lio/build && $(MAKE) -f CMakeFiles/Makefile2 faster-lio/src/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/faster_lio/build && $(MAKE) -f CMakeFiles/Makefile2 faster-lio/src/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/faster_lio/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
faster-lio/src/CMakeFiles/faster_lio.dir/rule:
	cd /home/<USER>/faster_lio/build && $(MAKE) -f CMakeFiles/Makefile2 faster-lio/src/CMakeFiles/faster_lio.dir/rule
.PHONY : faster-lio/src/CMakeFiles/faster_lio.dir/rule

# Convenience name for target.
faster_lio: faster-lio/src/CMakeFiles/faster_lio.dir/rule

.PHONY : faster_lio

# fast build rule for target.
faster_lio/fast:
	cd /home/<USER>/faster_lio/build && $(MAKE) -f faster-lio/src/CMakeFiles/faster_lio.dir/build.make faster-lio/src/CMakeFiles/faster_lio.dir/build
.PHONY : faster_lio/fast

laser_mapping.o: laser_mapping.cc.o

.PHONY : laser_mapping.o

# target to build an object file
laser_mapping.cc.o:
	cd /home/<USER>/faster_lio/build && $(MAKE) -f faster-lio/src/CMakeFiles/faster_lio.dir/build.make faster-lio/src/CMakeFiles/faster_lio.dir/laser_mapping.cc.o
.PHONY : laser_mapping.cc.o

laser_mapping.i: laser_mapping.cc.i

.PHONY : laser_mapping.i

# target to preprocess a source file
laser_mapping.cc.i:
	cd /home/<USER>/faster_lio/build && $(MAKE) -f faster-lio/src/CMakeFiles/faster_lio.dir/build.make faster-lio/src/CMakeFiles/faster_lio.dir/laser_mapping.cc.i
.PHONY : laser_mapping.cc.i

laser_mapping.s: laser_mapping.cc.s

.PHONY : laser_mapping.s

# target to generate assembly for a file
laser_mapping.cc.s:
	cd /home/<USER>/faster_lio/build && $(MAKE) -f faster-lio/src/CMakeFiles/faster_lio.dir/build.make faster-lio/src/CMakeFiles/faster_lio.dir/laser_mapping.cc.s
.PHONY : laser_mapping.cc.s

options.o: options.cc.o

.PHONY : options.o

# target to build an object file
options.cc.o:
	cd /home/<USER>/faster_lio/build && $(MAKE) -f faster-lio/src/CMakeFiles/faster_lio.dir/build.make faster-lio/src/CMakeFiles/faster_lio.dir/options.cc.o
.PHONY : options.cc.o

options.i: options.cc.i

.PHONY : options.i

# target to preprocess a source file
options.cc.i:
	cd /home/<USER>/faster_lio/build && $(MAKE) -f faster-lio/src/CMakeFiles/faster_lio.dir/build.make faster-lio/src/CMakeFiles/faster_lio.dir/options.cc.i
.PHONY : options.cc.i

options.s: options.cc.s

.PHONY : options.s

# target to generate assembly for a file
options.cc.s:
	cd /home/<USER>/faster_lio/build && $(MAKE) -f faster-lio/src/CMakeFiles/faster_lio.dir/build.make faster-lio/src/CMakeFiles/faster_lio.dir/options.cc.s
.PHONY : options.cc.s

pointcloud_preprocess.o: pointcloud_preprocess.cc.o

.PHONY : pointcloud_preprocess.o

# target to build an object file
pointcloud_preprocess.cc.o:
	cd /home/<USER>/faster_lio/build && $(MAKE) -f faster-lio/src/CMakeFiles/faster_lio.dir/build.make faster-lio/src/CMakeFiles/faster_lio.dir/pointcloud_preprocess.cc.o
.PHONY : pointcloud_preprocess.cc.o

pointcloud_preprocess.i: pointcloud_preprocess.cc.i

.PHONY : pointcloud_preprocess.i

# target to preprocess a source file
pointcloud_preprocess.cc.i:
	cd /home/<USER>/faster_lio/build && $(MAKE) -f faster-lio/src/CMakeFiles/faster_lio.dir/build.make faster-lio/src/CMakeFiles/faster_lio.dir/pointcloud_preprocess.cc.i
.PHONY : pointcloud_preprocess.cc.i

pointcloud_preprocess.s: pointcloud_preprocess.cc.s

.PHONY : pointcloud_preprocess.s

# target to generate assembly for a file
pointcloud_preprocess.cc.s:
	cd /home/<USER>/faster_lio/build && $(MAKE) -f faster-lio/src/CMakeFiles/faster_lio.dir/build.make faster-lio/src/CMakeFiles/faster_lio.dir/pointcloud_preprocess.cc.s
.PHONY : pointcloud_preprocess.cc.s

utils.o: utils.cc.o

.PHONY : utils.o

# target to build an object file
utils.cc.o:
	cd /home/<USER>/faster_lio/build && $(MAKE) -f faster-lio/src/CMakeFiles/faster_lio.dir/build.make faster-lio/src/CMakeFiles/faster_lio.dir/utils.cc.o
.PHONY : utils.cc.o

utils.i: utils.cc.i

.PHONY : utils.i

# target to preprocess a source file
utils.cc.i:
	cd /home/<USER>/faster_lio/build && $(MAKE) -f faster-lio/src/CMakeFiles/faster_lio.dir/build.make faster-lio/src/CMakeFiles/faster_lio.dir/utils.cc.i
.PHONY : utils.cc.i

utils.s: utils.cc.s

.PHONY : utils.s

# target to generate assembly for a file
utils.cc.s:
	cd /home/<USER>/faster_lio/build && $(MAKE) -f faster-lio/src/CMakeFiles/faster_lio.dir/build.make faster-lio/src/CMakeFiles/faster_lio.dir/utils.cc.s
.PHONY : utils.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... install/local"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... test"
	@echo "... faster_lio"
	@echo "... laser_mapping.o"
	@echo "... laser_mapping.i"
	@echo "... laser_mapping.s"
	@echo "... options.o"
	@echo "... options.i"
	@echo "... options.s"
	@echo "... pointcloud_preprocess.o"
	@echo "... pointcloud_preprocess.i"
	@echo "... pointcloud_preprocess.s"
	@echo "... utils.o"
	@echo "... utils.i"
	@echo "... utils.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/faster_lio/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

