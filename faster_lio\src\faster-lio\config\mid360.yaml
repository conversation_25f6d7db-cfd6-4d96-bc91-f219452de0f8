common:
  lid_topic: "/livox/lidar"
  imu_topic: "/livox/imu"
  time_sync_en: true          # Enable time synchronization for MID-360

preprocess:
  lidar_type: 4                # 4 for Livox MID-360 (generic PointCloud2 handler)
  scan_line: 6                 # MID-360 uses 6 scan lines like other Livox sensors
  blind: 0.5                     # MID-360 has better close-range performance, reduce blind zone
  time_scale: 1e-3             # Standard time scale for PointCloud2

mapping:
  acc_cov: 0.1
  gyr_cov: 0.1
  b_acc_cov: 0.0001
  b_gyr_cov: 0.0001
  fov_degree: 360              # MID-360 has 360° horizontal FOV
  det_range: 200.0             # MID-360 detection range up to 200m
  extrinsic_est_en: true       # Enable online extrinsic estimation for MID-360
  extrinsic_T: [ 0.0, 0.0, 0.0 ]  # Initial extrinsic translation (will be estimated online)
  extrinsic_R: [ 1, 0, 0,
                 0, 1, 0,
                 0, 0, 1 ]

publish:
  path_publish_en: true       # 启用路径发布，用于定位
  scan_publish_en: true       # false: close all the point cloud output
  scan_effect_pub_en: true    # true: publish the pointscloud of effect point
  dense_publish_en: false     # false: low down the points number in a global-frame point clouds scan.
  scan_bodyframe_pub_en: true # true: output the point cloud scans in IMU-body-frame

path_save_en: true            # 保存轨迹，用于精度计算和比较

pcd_save:
  pcd_save_en: true           # 建图模式保存地图
  interval: -1                # how many LiDAR frames saved in each pcd file;
  # -1 : all frames will be saved in ONE pcd file, may lead to memory crash when having too much frames.

feature_extract_enable: false
point_filter_num: 1           # MID-360 has higher point density, reduce filter number
max_iteration: 3
filter_size_surf: 0.2        # Smaller filter size for MID-360's higher resolution
filter_size_map: 0.2         # 暂时未用到，代码中为0， 即倾向于将降采样后的scan中的所有点加入map
cube_side_length: 1000

ivox_grid_resolution: 0.1     # Smaller grid resolution for MID-360's higher point density
ivox_nearby_type: 18          # 6, 18, 26
esti_plane_threshold: 0.1     # default=0.1
