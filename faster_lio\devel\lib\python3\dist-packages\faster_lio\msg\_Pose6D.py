# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from faster_lio/Pose6D.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct


class Pose6D(genpy.Message):
  _md5sum = "ab486e9c24704038320abf9ff59003d2"
  _type = "faster_lio/Pose6D"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """# the preintegrated Lidar states at the time of IMU measurements in a frame
float64  offset_time # the offset time of IMU measurement w.r.t the first lidar point
float64[3] acc       # the preintegrated total acceleration (global frame) at the Lidar origin
float64[3] gyr       # the unbiased angular velocity (body frame) at the Lidar origin
float64[3] vel       # the preintegrated velocity (global frame) at the Lidar origin
float64[3] pos       # the preintegrated position (global frame) at the Lidar origin
float64[9] rot       # the preintegrated rotation (global frame) at the Lidar origin"""
  __slots__ = ['offset_time','acc','gyr','vel','pos','rot']
  _slot_types = ['float64','float64[3]','float64[3]','float64[3]','float64[3]','float64[9]']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       offset_time,acc,gyr,vel,pos,rot

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(Pose6D, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.offset_time is None:
        self.offset_time = 0.
      if self.acc is None:
        self.acc = [0.] * 3
      if self.gyr is None:
        self.gyr = [0.] * 3
      if self.vel is None:
        self.vel = [0.] * 3
      if self.pos is None:
        self.pos = [0.] * 3
      if self.rot is None:
        self.rot = [0.] * 9
    else:
      self.offset_time = 0.
      self.acc = [0.] * 3
      self.gyr = [0.] * 3
      self.vel = [0.] * 3
      self.pos = [0.] * 3
      self.rot = [0.] * 9

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self.offset_time
      buff.write(_get_struct_d().pack(_x))
      buff.write(_get_struct_3d().pack(*self.acc))
      buff.write(_get_struct_3d().pack(*self.gyr))
      buff.write(_get_struct_3d().pack(*self.vel))
      buff.write(_get_struct_3d().pack(*self.pos))
      buff.write(_get_struct_9d().pack(*self.rot))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      start = end
      end += 8
      (self.offset_time,) = _get_struct_d().unpack(str[start:end])
      start = end
      end += 24
      self.acc = _get_struct_3d().unpack(str[start:end])
      start = end
      end += 24
      self.gyr = _get_struct_3d().unpack(str[start:end])
      start = end
      end += 24
      self.vel = _get_struct_3d().unpack(str[start:end])
      start = end
      end += 24
      self.pos = _get_struct_3d().unpack(str[start:end])
      start = end
      end += 72
      self.rot = _get_struct_9d().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self.offset_time
      buff.write(_get_struct_d().pack(_x))
      buff.write(self.acc.tostring())
      buff.write(self.gyr.tostring())
      buff.write(self.vel.tostring())
      buff.write(self.pos.tostring())
      buff.write(self.rot.tostring())
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      start = end
      end += 8
      (self.offset_time,) = _get_struct_d().unpack(str[start:end])
      start = end
      end += 24
      self.acc = numpy.frombuffer(str[start:end], dtype=numpy.float64, count=3)
      start = end
      end += 24
      self.gyr = numpy.frombuffer(str[start:end], dtype=numpy.float64, count=3)
      start = end
      end += 24
      self.vel = numpy.frombuffer(str[start:end], dtype=numpy.float64, count=3)
      start = end
      end += 24
      self.pos = numpy.frombuffer(str[start:end], dtype=numpy.float64, count=3)
      start = end
      end += 72
      self.rot = numpy.frombuffer(str[start:end], dtype=numpy.float64, count=9)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_3d = None
def _get_struct_3d():
    global _struct_3d
    if _struct_3d is None:
        _struct_3d = struct.Struct("<3d")
    return _struct_3d
_struct_9d = None
def _get_struct_9d():
    global _struct_9d
    if _struct_9d is None:
        _struct_9d = struct.Struct("<9d")
    return _struct_9d
_struct_d = None
def _get_struct_d():
    global _struct_d
    if _struct_d is None:
        _struct_d = struct.Struct("<d")
    return _struct_d
