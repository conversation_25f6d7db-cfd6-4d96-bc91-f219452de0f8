<?xml version="1.0"?>
<robot name="lio" xmlns:xacro="http://tixiaoshan.github.io/">
  <xacro:property name="PI" value="3.1415926535897931" />

  <link name="chassis_link"></link>

  <link name="base_link"></link>
  <joint name="base_link_joint" type="fixed">
    <parent link="base_link"/>
    <child link="chassis_link" />
    <origin xyz="0 0 0" rpy="0 0 0" />
  </joint>

  <link name="imu_link"> </link>
  <joint name="imu_joint" type="fixed">
    <parent link="chassis_link" />
    <child link="imu_link" />
    <origin xyz="0 0 0" rpy="0 0 0" />
  </joint>

  <link name="velodyne"> </link>
  <joint name="velodyne_joint" type="fixed">
    <parent link="chassis_link" />
    <child link="velodyne" />
    <origin xyz="0 0 0" rpy="0 0 0" />
  </joint>

  <link name="navsat_link"> </link>
  <joint name="navsat_joint" type="fixed">
    <parent link="chassis_link" />
    <child link="navsat_link" />
    <origin xyz="0 0 0" rpy="0 0 0" />
  </joint>

</robot>
