# generated from genmsg/cmake/pkg-genmsg.context.in

messages_str = "/home/<USER>/faster_lio/src/faster-lio/thirdparty/livox_ros_driver/msg/CustomPoint.msg;/home/<USER>/faster_lio/src/faster-lio/thirdparty/livox_ros_driver/msg/CustomMsg.msg"
services_str = ""
pkg_name = "livox_ros_driver"
dependencies_str = "std_msgs"
langs = "gencpp;geneus;genlisp;gennodejs;genpy"
dep_include_paths_str = "livox_ros_driver;/home/<USER>/faster_lio/src/faster-lio/thirdparty/livox_ros_driver/msg;std_msgs;/opt/ros/noetic/share/std_msgs/cmake/../msg"
PYTHON_EXECUTABLE = "/usr/bin/python3"
package_has_static_sources = '' == 'TRUE'
genmsg_check_deps_script = "/opt/ros/noetic/share/genmsg/cmake/../../../lib/genmsg/genmsg_check_deps.py"
